package com.caidaocloud.certificate.service.certificate.application.service;

import com.caidaocloud.certificate.service.certificate.domain.base.enums.BusinessEventTypeEnum;
import com.caidaocloud.certificate.service.certificate.domain.base.util.ObjectConvertUtil;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateDo;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateTypeDo;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateTypeQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.CertificateTypeVo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectUtil;
import com.caidaocloud.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/9 18:47
 **/
@Service
@Slf4j
public class CertificateTypeService {

    @Resource
    private CertificateTypeDo certificateTypeDo;
    @Resource
    private CertificateDo certificateDo;

    public List getList(CertificateTypeQueryDto dto) {
        List<CertificateTypeDo> tempList=new ArrayList<>();
        if (StringUtils.isNotEmpty(dto.getName())){
            Map<Integer, List<CertificateTypeDo>> collect = null;
            try {
                collect = selectList(dto).stream().collect(Collectors.groupingBy(CertificateTypeDo::getLevel));
            } catch (Exception e) {
                log.error(e.toString());
                e.printStackTrace();
            }
            List<CertificateTypeVo> list = putProperties(collect.get(1)==null?tempList:collect.get(1), dto);
            List<CertificateTypeVo> level2 = putPropertiesLevel2(collect.get(2)==null?tempList:collect.get(2),dto);
            list.addAll(level2);
            return  list.stream()
                    .collect(Collectors.collectingAndThen(Collectors.toMap(CertificateTypeVo::getBid, entity -> entity, (entity1, entity2) -> entity1),
                            map -> new ArrayList<>(map.values())));
        }else {
            dto.setLevel(1);
            var level1List = selectList(dto);
            List<CertificateTypeVo> dataList=putProperties(level1List,dto);
            return dataList;
        }

    }

    private List<CertificateTypeVo>  putProperties(List<CertificateTypeDo> level1List,CertificateTypeQueryDto dto) {
//        HttpServletRequest request = WebUtil.getRequest();
//        String header = request.getHeader("Accept-Language");

        List<CertificateTypeVo> voList = new ArrayList<>();
        Set<String> processedBids = new HashSet<>(); // 用于存储已经处理过的 bid

        for (CertificateTypeDo t : level1List) {
            if (!processedBids.contains(t.getBid())) { // 如果尚未处理过这个 bid
                processedBids.add(t.getBid());
                CertificateTypeVo vo = new CertificateTypeVo();
                BeanUtil.copyProperties(t, vo);
                dto.setPBid(t.getBid());
                dto.setLevel(2);
                List<CertificateTypeDo> sublist = selectList(dto);
                List<CertificateTypeVo> partList = ObjectConvertUtil.convertList(sublist, CertificateTypeVo.class,
                        (t1, v1) -> {
                            v1.setI18nName(FastjsonUtil.toObject(t1.getI18nName(), Map.class));
//                            if (v1.getI18nName()!=null){
//                                v1.setName(v1.getI18nName().get(header)!=null? (String) v1.getI18nName().get(header) :"");
//                            }
                        });
                vo.setChildren(partList);
                vo.setI18nName(FastjsonUtil.toObject(t.getI18nName(), Map.class));
                voList.add(vo);
            }
        }
        List<CertificateTypeVo> collect = voList.stream().distinct().collect(Collectors.toList());
        return collect;

    }
    private List<CertificateTypeVo>  putPropertiesLevel2(List<CertificateTypeDo> level2List,CertificateTypeQueryDto dto) {
//        HttpServletRequest request = WebUtil.getRequest();
//        String header = request.getHeader("Accept-Language");

        Map<String,CertificateTypeVo> resuleMap = new HashMap<>();
        List<CertificateTypeVo> partList = ObjectConvertUtil.convertList(level2List, CertificateTypeVo.class,
                (t1,v1)->{
                    v1.setI18nName(FastjsonUtil.toObject(t1.getI18nName(), Map.class));
//                    if (v1.getI18nName()!=null){
//                        v1.setName(v1.getI18nName().get(header)!=null? (String) v1.getI18nName().get(header) :"");
//                    }
                });
        Map<Integer,CertificateTypeDo> pbidMap = new HashMap<>();
        for (CertificateTypeVo vo: partList) {
            CertificateTypeDo pData;
            if (!pbidMap.containsKey(vo.getPBid())){
                pData = getById(vo.getPBid());
            }else {

                pData=pbidMap.get(vo.getPBid());
            }
            CertificateTypeDo data = getById(vo.getBid());

            CertificateTypeVo vo2 = ObjectConvertUtil.convert(pData, CertificateTypeVo.class, vo1 -> {
                vo1.setI18nName(FastjsonUtil.toObject(data.getI18nName(), Map.class));
//                if (vo1.getI18nName()!=null){
//                    vo1.setName(vo1.getI18nName().get(header)!=null? (String) vo1.getI18nName().get(header) :"");
//                }
            });
            CertificateTypeVo child = ObjectConvertUtil.convert(data, CertificateTypeVo.class, vo1 -> {
                vo1.setI18nName(FastjsonUtil.toObject(data.getI18nName(), Map.class));
//                if (vo1.getI18nName()!=null){
//                    vo1.setName(vo1.getI18nName().get(header)!=null? (String) vo1.getI18nName().get(header) :"");
//                }
            });
            vo2.setChildren(Arrays.asList(child));
            resuleMap.put(vo2.getBid(),vo2);
        }
        return resuleMap.values().stream().collect(Collectors.toList());
    }

    public String add(CertificateTypeDo data) {
        checkLevel(data);
       return certificateTypeDo.save(data);
    }

    /**
     * 检查层级关系
     * @param data
     */
    private void checkLevel(CertificateTypeDo data) {
        if (data.getPBid()==null){
            //证书类型别
            data.setLevel(1);
        }else {
            data.setLevel(2);
        }
    }

    public void update(CertificateTypeDo dto) {
        certificateTypeDo.update(dto);
    }

    public List<CertificateTypeDo> selectList(CertificateTypeQueryDto dto) {
        HttpServletRequest request = WebUtil.getRequest();
        String header = request.getHeader("Accept-Language");

        List<CertificateTypeDo> dataList = certificateTypeDo.selectList(dto);
        dataList.forEach(t->{
            if (t.getI18nName()!=null){
                Map map = FastjsonUtil.toObject(t.getI18nName(), Map.class);
                if (map!=null){
                    t.setName(map.get(header)!=null? (String) map.get(header) :(String) map.get("default"));
                }

            }
        });
        return dataList;
    }

    public void delete(CertificateTypeDo data) {
        PreCheck.preCheckArgument(ObjectUtil.isEmpty(certificateTypeDo.selectById(data.getBid())), "bid为空");
        CertificateTypeQueryDto queryDto=new CertificateTypeQueryDto();
        queryDto.setPBid(data.getBid());
        List<CertificateTypeDo> exitList = certificateTypeDo.selectList(queryDto);
        PreCheck.preCheckArgument(CollectionUtils.isNotEmpty(exitList), "请先删除该类别下的子类数据");
        CertificateQueryDto dto=new CertificateQueryDto();
        dto.setTypeBid(data.getBid());
        List<CertificateDo> certificateDos = certificateDo.selectList(dto);
        PreCheck.preCheckArgument(CollectionUtils.isNotEmpty(certificateDos), "请先删除该子类下的证书");

        certificateTypeDo.delete(data);

    }

    public void enable(CertificateTypeDo data) {
        certificateTypeDo.updateStatus(data, BusinessEventTypeEnum.ENABLE);
    }

    public void disable(CertificateTypeDo data) {
        PreCheck.preCheckArgument(ObjectUtil.isEmpty(certificateTypeDo.selectById(data.getBid())), "bid为空");
        CertificateTypeQueryDto queryDto=new CertificateTypeQueryDto();
        queryDto.setPBid(data.getBid());

        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(null == data.getStatus() ? null : data.getStatus().toString());
        queryDto.setStatus("0");
        List<CertificateTypeDo> exitList = certificateTypeDo.selectList(queryDto);
        PreCheck.preCheckArgument(CollectionUtils.isNotEmpty(exitList), "请先停用该类别下的子类");
        CertificateQueryDto dto=new CertificateQueryDto();
        dto.setTypeBid(data.getBid());
        List<CertificateDo> certificateDos = certificateDo.selectList(dto);
        PreCheck.preCheckArgument(CollectionUtils.isNotEmpty(certificateDos), "请先停用该子类下的证书");
        certificateTypeDo.updateStatus(data, BusinessEventTypeEnum.DISABLE);
    }

    public CertificateTypeDo getById(String bid) {
        return certificateTypeDo.selectById(bid);
    }
}
