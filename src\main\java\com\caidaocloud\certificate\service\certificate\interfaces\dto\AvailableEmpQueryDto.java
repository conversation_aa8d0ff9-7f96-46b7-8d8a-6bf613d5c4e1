package com.caidaocloud.certificate.service.certificate.interfaces.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询拥有指定证书且可使用的员工DTO
 * 
 * <AUTHOR> Assistant
 * @date 2024/12/19
 */
@Data
@ApiModel(description = "查询拥有指定证书且可使用的员工DTO")
public class AvailableEmpQueryDto {
    
    @ApiModelProperty(value = "证书ID", required = true)
    private String certificateId;
    
    @ApiModelProperty(value = "开始时间（时间戳）", required = false)
    private Long startTime;
    
    @ApiModelProperty(value = "结束时间（时间戳）", required = false)
    private Long endTime;
}
