### 测试证书类型和证书树形结构接口
POST http://localhost:8080/certificateType/getTreeData
Content-Type: application/json
Accept-Language: zh-CN

{
  "status": "0"
}

### 测试证书类型和证书树形结构接口 - 英文
POST http://localhost:8080/certificateType/getTreeData
Content-Type: application/json
Accept-Language: en-US

{
  "status": "0"
}

### 测试证书分页查询 - 不指定typeBid（使用join查询）
POST http://localhost:8080/certificate/pageList
Content-Type: application/json
Accept-Language: zh-CN

{
  "pageNo": 1,
  "pageSize": 10,
  "status": "0"
}

### 测试证书分页查询 - 指定typeBid（使用原有查询）
POST http://localhost:8080/certificate/pageList
Content-Type: application/json
Accept-Language: zh-CN

{
  "pageNo": 1,
  "pageSize": 10,
  "status": "0",
  "typeBid": "specific-type-bid"
}

### 测试证书分页查询 - 带名称或编码搜索（使用join查询）
POST http://localhost:8080/certificate/pageList
Content-Type: application/json
Accept-Language: zh-CN

{
  "pageNo": 1,
  "pageSize": 10,
  "status": "0",
  "nameOrCode": "测试"
}
