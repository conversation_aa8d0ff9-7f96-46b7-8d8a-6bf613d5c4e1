package com.caidaocloud.certificate.service.certificate.domain.entity;

import com.caidaocloud.certificate.service.certificate.domain.base.entity.DataEntity;
import com.caidaocloud.certificate.service.certificate.domain.base.enums.BusinessEventTypeEnum;
import com.caidaocloud.certificate.service.certificate.domain.base.util.UserContext;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICeritificateAndEmpRepository;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CeritificateAndEmpQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.QualifiedPerQueryDto;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.util.SpringUtil;
import groovy.util.logging.Slf4j;
import lombok.Data;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/15 13:35
 *  证书人员管理类
 **/
@Data
@Service
@Slf4j
public class CeritificateAndEmpDo extends DataEntity {
    public static final String IDENTIFIER = "entity.certificate.certificateAndEmp";
    /**
     * bid
     */
    private String bid;
    /**
     * 员工id
     */
    private String empId;
    /**
     * 证书类型
     */
    private String typeName;
    /**
     * 证书类型bid
     */
    private String typeBid;
    /**
     * 证书类型
     */
    private String typeSubName;
    /**
     * 证书类型bid
     */
    private String typeSubBid;
    /**
     * 证书名称
     */
    private String ceritifiCateName;
    /**
     * 证书编号
     */
    private String ceritifiCateCode;
    /**
     * 证书id
     */
    private String ceritifiCateBid;
    /**
     * 取得日期
     */
    private Long acquiredTime;
    /**
     * 注册日期
     */
    private String registerTime;
    /**
     * 失效日期
     */
    private String expireTime;
    /**
     * 注册号
     */
    private String registrationNo;
    /**
     * 专业
     */
    private String specialty;
    /**
     * 专业多语言字段
     */
    private String i18nName;
    /**
     * 签发地
     */
    private DictSimple issueAt;
    private String issueAuthority;
    /**
     * 入省备案
     */
    private DictSimple accessProvince;
    /**
     * 是否限制使用
     */
    private EnumSimple isuse;
    /**
     * 是否注册
     */
    private EnumSimple isRegister;
    /**
     * 备注
     */
    private String remake;
    /**
     * 附件
     */
    private Attachment attachFile;
    /**
     * 项目
     */
    private String proBid;
    /**
     *
     *组织id
     */
    private String organizeId;
    /**
     *
     *证书有效状态
     */
    private EnumSimple approveStatus;
    /**
     *
     *表单id
     */
    private String formId;
    /**
     *
     *表单数据
     */
    private String formData;
    /**
     *
     *证书使用状态
     */
    private EnumSimple useStatus;
    /**
     *
     *持证数量
     */
    private Integer certifiCateNums;
//    ______**************_____权限范围附加字段
    /**
     * 工作地
     */
    private String workPlace;
    /**
     * 公司
     */
    private String company;
    /**
     * 合同类型
     */
    private DictSimple contractSettingType;
    /**
     * 员工类型
     */
    private DictSimple empType;


    public static List<CeritificateAndEmpDo> lisyByEmpId(String empId) {
        return SpringUtil.getBean(ICeritificateAndEmpRepository.class).listByEmpId(empId, null);
    }

    public void delete(CeritificateAndEmpDo vo) {
        vo.setIdentifier(IDENTIFIER);
        SpringUtil.getBean(ICeritificateAndEmpRepository.class).delete(vo);
    }

    public String update(CeritificateAndEmpDo data) {
        CeritificateAndEmpDo dbData = selectById(data.getBid());
        DataEntity.initFieldValue(IDENTIFIER, BusinessEventTypeEnum.UPDATE, data, dbData);
        int result = SpringUtil.getBean(ICeritificateAndEmpRepository.class).updateById(data);
        return result==0?data.getBid():"-1";
    }

    public CeritificateAndEmpDo selectById(String bid) {
        return SpringUtil.getBean(ICeritificateAndEmpRepository.class).selectById(bid,IDENTIFIER);
    }

    public String save(CeritificateAndEmpDo data) {
        UserInfo userInfo = UserContext.preCheckUser();
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
        data.setCreateBy(userId);
        data.setCreateTime(System.currentTimeMillis());
        data.setUpdateBy(userId);
        data.setUpdateTime(data.getCreateTime());
        data.setBid(SnowUtil.nextId());
        data.setIdentifier(IDENTIFIER);

        String dataId = DataInsert.identifier(data.getIdentifier()).insert(data);
        return dataId;
    }
    public Long count(CeritificateAndEmpQueryDto data) {

        return DataQuery.identifier(CeritificateAndEmpDo.IDENTIFIER)
                .limit(-1, 1).count(DataFilter.eq("ceritifiCateName", data.getCeritifiCateName()), System.currentTimeMillis());
    }

    /**
     * 查询全部
     */
    public List<CeritificateAndEmpDo> selectAll(QualifiedPerQueryDto data) {
        return SpringUtil.getBean(ICeritificateAndEmpRepository.class).select(data);
    }

    public boolean checkActive(long time) {
        if (expireTime == null) {
            EnumSimple enumSimple = new EnumSimple();
            enumSimple.setText("有效");
            enumSimple.setValue("0");
            setStatus(enumSimple);
            return true;
        } else {
            if (time < Long.parseLong(expireTime)) {
                EnumSimple enumSimple = new EnumSimple();
                enumSimple.setText("有效");
                enumSimple.setValue("0");
                setStatus(enumSimple);
                return true;
            }
            else {
                EnumSimple enumSimple = new EnumSimple();
                enumSimple.setText("无效");
                enumSimple.setValue("1");
                setStatus(enumSimple);
                return false;
            }
        }
    }


}
