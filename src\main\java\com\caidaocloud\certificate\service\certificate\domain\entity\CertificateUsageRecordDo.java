package com.caidaocloud.certificate.service.certificate.domain.entity;

import java.util.Optional;

import com.caidaocloud.certificate.service.certificate.domain.base.entity.DataEntity;
import com.caidaocloud.certificate.service.certificate.domain.enums.CertificateStatus;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICertificateUsageRecordRepository;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateUsageRecordQueryDto;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2024/4/15
 */
@Data
public class CertificateUsageRecordDo extends DataEntity {
	private String projectId;
	// 证书类别bid
	private String certificate;
	// 登记类别
	private DictSimple type;
	private Long startDate;
	private Long endDate;
	// 人员证书bid
	private String empCertificate;
	// 员工id
	private String empId;
	private CertificateStatus certificateStatus;
	private String remark;

	public static final String IDENTIFIER = "entity.certificate.CertificateUsageRecord";

	public CertificateUsageRecordDo() {
		setIdentifier(IDENTIFIER);
	}

	public static Optional<CertificateUsageRecordDo> findLockedByEmpCertificate(String empCertificate) {
		return SpringUtil.getBean(ICertificateUsageRecordRepository.class)
				.selectUsageRecordByCertificate(null, empCertificate, CertificateStatus.LOCKED).getItems().stream().findFirst();
	}

	public static Optional<CertificateUsageRecordDo> findByEmpCertificate(String empCertificate) {
		return SpringUtil.getBean(ICertificateUsageRecordRepository.class)
				.selectUsageRecordByCertificate(null, empCertificate, null).getItems().stream().findFirst();

	}



	public static PageResult<CertificateUsageRecordDo> loadByTime(long currentTimestamp, int pageSize, int pageNo) {
		return SpringUtil.getBean(ICertificateUsageRecordRepository.class).selectUsageRecordPageByTime(currentTimestamp,pageSize,pageNo);
	}

	public CertificateUsageRecordDo save() {
		setUpdateTime(System.currentTimeMillis());
		setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
		if (getCreateTime() == 0L) {
			setCreateTime(getUpdateTime());
			setCreateBy(getUpdateBy());
		}
		updateStatus();
		checkUsage();
		return SpringUtil.getBean(ICertificateUsageRecordRepository.class).save(this);
	}

	public void updateStatus() {
		setCertificateStatus(CertificateStatus.determineProjectStatus(startDate, endDate));
	}

	private void checkUsage() {
		PageResult<CertificateUsageRecordDo> exists = SpringUtil.getBean(ICertificateUsageRecordRepository.class)
				.selectUsageRecordByCertificate(getBid(), empCertificate,null);
		for (CertificateUsageRecordDo item : exists.getItems()) {
			// 获取数据库记录中的起始日期和结束日期
			long recordStartDate = item.getStartDate();
			long recordEndDate = item.getEndDate();

			if ((recordStartDate >= startDate && recordStartDate <= endDate) || (recordEndDate >= startDate && recordEndDate <= endDate) || (startDate >= recordStartDate && startDate <= recordEndDate) || (endDate >= recordStartDate && endDate <= recordEndDate)) {
				throw new ServerException("The given date range overlaps with an existing usage record.project id=" + item.getProjectId());
			}
		}
	}

	public void delete() {
		SpringUtil.getBean(ICertificateUsageRecordRepository.class).delete(this);
	}

	public static Optional<CertificateUsageRecordDo> findById(String bid) {
		return Optional.ofNullable(SpringUtil.getBean(ICertificateUsageRecordRepository.class)
				.selectById(bid, IDENTIFIER));
	}

	public static PageResult<CertificateUsageRecordDo> page(CertificateUsageRecordQueryDto dto) {
		return SpringUtil.getBean(ICertificateUsageRecordRepository.class).selectPage(dto);
	}
}
