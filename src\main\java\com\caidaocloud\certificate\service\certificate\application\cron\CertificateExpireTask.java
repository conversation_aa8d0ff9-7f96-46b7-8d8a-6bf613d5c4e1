package com.caidaocloud.certificate.service.certificate.application.cron;

import com.caidaocloud.certificate.service.certificate.application.dto.MsgBusinessConfigDto;
import com.caidaocloud.certificate.service.certificate.application.dto.TenantDto;
import com.caidaocloud.certificate.service.certificate.application.feign.MaintenanceFeignClient;
import com.caidaocloud.certificate.service.certificate.application.service.CertificateExpireService;
import com.caidaocloud.certificate.service.certificate.application.service.CertificateSettingService;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateSettingDo;
import com.caidaocloud.message.sdk.dto.MsgConfigDto;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.sdk.service.MsgNoticeService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.DateUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.List;

/**
 * 证书到期提醒
 * <AUTHOR> Zhou
 * @date 2024/5/13
 */
@Component
@Slf4j
public class CertificateExpireTask {
	@Resource
	private MaintenanceFeignClient maintenanceFeignClient;
	@Resource
	private CertificateExpireService certificateExpireService;
	@Resource
	private MsgNoticeService msgNoticeService;
	@Resource
	private CertificateSettingService certificateSettingService;

	@XxlJob("CertificateExpireJobHandler")
	public ReturnT<String> notice() {
		XxlJobHelper.log("XxlJob CertificateExpireJobHandler start");
		log.info("XxlJob CertificateExpireJobHandler start");
		long currentTimestamp = DateUtil.getCurrentTimestamp();
		for (TenantDto tenantDto : maintenanceFeignClient.tenantList().getData()) {
			try {
				SecurityUserInfo userInfo = new SecurityUserInfo();
				userInfo.setTenantId(tenantDto.getTenantId());
				userInfo.setUserId(0L);
				userInfo.setEmpId(0L);
				SecurityUserUtil.setSecurityUserInfo(userInfo);

				List<MsgConfigDto> msgConfigList = msgNoticeService.getMsgConfigList(NoticeType.CERTIFICATE_EXPIRE_NOTICE);
				if (msgConfigList.isEmpty()) {
					continue;
				}
				CertificateSettingDo data=new CertificateSettingDo();
				data.setTypeCode("msg");
				List<CertificateSettingDo> settingDtos = (List<CertificateSettingDo>)certificateSettingService.selectList(data);

				List<MsgBusinessConfigDto> list = new ArrayList<>();
				for (CertificateSettingDo settingDto : settingDtos) {
					list.add(new MsgBusinessConfigDto(settingDto.getCertificateBid(),settingDto.getMsgBid()));
				}
				certificateExpireService.notice(msgConfigList, list, currentTimestamp);
			}
			catch (Exception e) {
				log.error("XxlJob CertificateExpireJobHandler error,tenantId={}", tenantDto.getTenantId(), e);
			}
			finally {
				SecurityUserUtil.removeSecurityUserInfo();
			}
		}


		log.info("XxlJob CertificateExpireJobHandler end");
		XxlJobHelper.log("XxlJob CertificateExpireJobHandler finished");
		return ReturnT.SUCCESS;
	}

}
