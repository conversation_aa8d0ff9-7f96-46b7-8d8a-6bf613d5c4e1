package com.caidaocloud.certificate.service.certificate.application.service;

import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.caidaocloud.certificate.service.certificate.application.dto.CertificateCardDto;
import com.caidaocloud.certificate.service.certificate.application.dto.EmpInfoDto;
import com.caidaocloud.certificate.service.certificate.application.dto.EmpSearchDto;
import com.caidaocloud.certificate.service.certificate.application.feign.IHrWorkFeign;
import com.caidaocloud.certificate.service.certificate.application.feign.MasterDataEmpInfoFeign;
import com.caidaocloud.certificate.service.certificate.domain.base.util.ExcelUtils;
import com.caidaocloud.certificate.service.certificate.domain.base.util.LangUtil;
import com.caidaocloud.certificate.service.certificate.domain.base.util.TagProperty;
import com.caidaocloud.certificate.service.certificate.domain.base.util.TimeUtils;
import com.caidaocloud.certificate.service.certificate.domain.base.util.UserContext;
import com.caidaocloud.certificate.service.certificate.domain.entity.*;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICeritificateAndEmpRepository;
import com.caidaocloud.certificate.service.certificate.domain.repository.IEmpCertificateRepository;
import com.caidaocloud.certificate.service.certificate.infrastructure.respository.impl.CertificateRepositoryImpl;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.*;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.CeritificateTopVo;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.CertificateTypeVo;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.QualifiedPerFunVO;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.QualifiedPerVo;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.org.OrgVo;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.dto.AbstractData;
import com.caidaocloud.masterdata.dto.EmpWorkInfoVo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/22 14:00
 **/
@Service
@Slf4j
public class QualifiedPerService {
    @Autowired
    private EmpCertificateDo empCertifateDo;
    @Autowired
    private ICeritificateAndEmpRepository ceritificateAndEmpRepository;
    @Autowired
    private CertificateDo certificateDo;
    @Resource
    private IHrWorkFeign iHrWorkFeign;
    @Resource
    private CertificateSettingService certificateSettingService;
    @Autowired
    private CertificateTypeDo certificateTypeDo;
    @Autowired
    private CertificateRepositoryImpl certificateRepository;

    @Autowired
    private CertificateTypeService certificateTypeService;

    @Value("${postTxt.showCode:enabled}")
    private String postTxtShowCode;

    // 分页查询
    public PageResult<Map<String, Object>> pageList(CeritificateAndEmpQueryDto queryDto, String header) {
        // 按表头过滤
        if (queryDto.isFilterAvailable()) {
            List<CeritificateTopVo> headerList = empCertifateDo
                    .query(String.valueOf(SecurityUserUtil.getSecurityUserInfo()
                            .getUserId()));
            List<String> headerCertificateBids = Sequences.sequence(headerList)
                    .flatMap(typeHeader -> Sequences.sequence(typeHeader.getChildren()).map(CeritificateTopVo::getBid))
                    .toList();
            queryDto.setBids(headerCertificateBids);
        }
        PageResult<CertificateCardDto> page = ceritificateAndEmpRepository.queryCardPage(queryDto,
                queryDto.getPageSize(), queryDto.getPageNo(), queryDto.isFilterAvailable());
        List<CertificateCardDto> items = page.getItems();

        Map<String, Object> empTitle = new LinkedHashMap<>();
        Map<String, Object> title = new LinkedHashMap<>();
        List<Map<String, Object>> result = Lists.list();
        // 处理员工信息表头
        initEmpTitle(empTitle);
        // 处理证书表头
        initactivit(title, header);

        Map<String, Object> titleId = new HashMap<>();
        initactivitId(titleId);

        List<String> empIdList = items.stream().map(CertificateCardDto::getEmpId).distinct()
                .collect(Collectors.toList());
        // Map<String, EmpWorkInfoVo> EmpMap = getEntityBatch(empIdList, convert);

        List<Map> empInfoListSimple = SpringUtil.getBean(IHrWorkFeign.class)
                .listEmpInfoSimple(empIdList, TimeUtils.getStartOfDayTimestamp()).getData();

        items.parallelStream().forEach(t -> {
            Map<String, Object> data = new LinkedHashMap<>();
            val empId = t.getEmpId();
            empInfoListSimple.stream().filter(empInfoSimple -> {
                val workInfo = (Map) empInfoSimple.get("empWorkInfo");
                val workNo = (String) workInfo.get("workno");
                val name = (String) workInfo.get("name");
                val workInfoEmpId = (String) workInfo.get("empId");
                return empId.equals(workInfoEmpId) &&
                        (StringUtils.isEmpty(queryDto.getNameOrNo())
                                || StringUtils.trimToEmpty(name).contains(queryDto.getNameOrNo())
                                || StringUtils.trimToEmpty(workNo).contains(queryDto.getNameOrNo()));
            }).findFirst().ifPresent(empInfoSimple -> {
                initData(title, data, empId, empInfoSimple);
                // 处理动态证书相关数据
                initActivityData(data, t, titleId);
                data.put("empTitle", empTitle);
                data.put("title", title);
                result.add(data);
            });
        });
        return new PageResult(result, queryDto.getPageNo(), queryDto.getPageSize(), page.getTotal());
    }

    private void initactivitId(Map<String, Object> title) {
        CertificateTypeQueryDto typeQueryDto = new CertificateTypeQueryDto();
        typeQueryDto.setStatus("0");
        List<CertificateTypeVo> list = certificateTypeService.getList(typeQueryDto);
        List<String> typeBidList = Sequences.sequence(list)
                .flatMap(CertificateTypeVo::getChildren).map(AbstractData::getBid).toList();
        CertificateQueryDto dto = new CertificateQueryDto();
        dto.setStatus("0");
        String userId = UserContext.getUserId();
        List<CeritificateTopVo> query = empCertifateDo.query(userId);
        List<String> cerBids = new ArrayList<>();
        for (CeritificateTopVo topVo : query) {
            if (topVo.getChildren() == null) {
                continue;
            }
            for (CeritificateTopVo child : topVo.getChildren()) {
                cerBids.add(child.getBid());
            }
        }
        List<CertificateDo> cerDoList;
        if (cerBids.size() > 0) {
            cerDoList = certificateRepository.selectListByIds(cerBids);
        } else {
            // 全部可用证书集合
            cerDoList = certificateDo.selectList(dto);
        }

        cerDoList.sort((cerDo1, cerDo2) -> {
            val type1 = typeBidList.indexOf(cerDo1.getTypeBid());
            val type2 = typeBidList.indexOf(cerDo2.getTypeBid());
            if (type1 == type2) {
                Integer sort1 = Optional.ofNullable(cerDo1.getSortNum()).orElse(0);
                Integer sort2 = Optional.ofNullable(cerDo2.getSortNum()).orElse(0);
                return sort1.compareTo(sort2);
            }
            else {
                return Integer.compare(type1, type2);
            }
        });
        List<String> cerList = cerDoList.stream().map(CertificateDo::getBid).distinct().collect(Collectors.toList());
        // if (cerBids.size()>0){
        // cerList =
        // certificateRepository.selectListByIds(cerBids).stream().map(CertificateDo::getI18nName).distinct().collect(Collectors.toList());
        // }else {
        // //全部可用证书集合
        // cerList =
        // certificateDo.selectList(dto).stream().map(CertificateDo::getI18nName).distinct().collect(Collectors.toList());
        // }

        int num = 0;
        for (Object t : cerList) {
            num++;
            title.put("certificateName" + (num), t);
        }
    }

    private Map<String, EmpWorkInfoVo> getEntityBatch(List<String> empIdList, CeritificateAndEmpQueryDto convert) {
        EmpSearchDto dto = new EmpSearchDto();
        dto.setEmpIds(empIdList);
        dto.setDatetime(System.currentTimeMillis());
        List<EmpWorkInfoVo> dataList = new ArrayList<>();
        empIdList.forEach(t -> {
            EmpWorkInfoVo entity = getEntity(t);
            dataList.add(entity);
        });
        Map<String, EmpWorkInfoVo> empInfoDtoMap = dataList.stream().filter(t -> {
            if (StringUtils.isNotEmpty(convert.getNameOrNo())) {
                return t.getName().contains(convert.getNameOrNo()) || t.getWorkno().contains(convert.getNameOrNo());
            }
            return true;
        }

        ).collect(Collectors.toMap(EmpWorkInfoVo::getEmpId, t -> t));
        return empInfoDtoMap.size() == 0 ? null : empInfoDtoMap;

    }

    EmpWorkInfoVo getEntity(String empId) {
        EmpWorkInfoVo data = SpringUtil.getBean(MasterDataEmpInfoFeign.class)
                .loadEmpWorkInfo(empId, System.currentTimeMillis()).getData();
        EmpSearchDto dto = new EmpSearchDto();
        dto.setEmpIds(Arrays.asList(empId));
        EmpInfoDto vo = SpringUtil.getBean(MasterDataEmpInfoFeign.class).loadEmpInfoList(dto).getData().get(0);
        data.setJobTxt(vo.getJobTxt());
        com.caidaocloud.hr.service.vo.EmpWorkInfoVo infoVo = iHrWorkFeign
                .getEmpWorkInfo(empId, System.currentTimeMillis()).getData();
        data.setSocialSecurity(infoVo.getSocialSecurity());
        data.setJobGrade(infoVo.getJobGrade());
        OrgVo data1 = iHrWorkFeign.getDetail(infoVo.getOrganize(), System.currentTimeMillis()).getData();
        if (data.getLeadEmpId() != null)
            data.setLeadEmpId(data1.getLeaderEmp());
        return data;
    }

    // /**
    // * 处理动态数据
    // */
    // private void initActtivityData(Map<String, Object> data, CertificateCardDto
    // dto, Map<String, Object> title) {
    // AtomicInteger num = new AtomicInteger(0);
    // dto.getCeritificateList().forEach(t -> {
    // QualifiedPerFunVO qualifiedPerFunVO = new QualifiedPerFunVO();
    // List<Boolean> booleans = checkColorAndUse(t);
    // qualifiedPerFunVO.setIsUse(booleans.get(0));
    // qualifiedPerFunVO.setIsColor(booleans.get(1));
    // qualifiedPerFunVO.setValue(t.getCeritifiCateName());
    // for (Map.Entry<String, Object> entry : title.entrySet()) {
    // if (entry.getValue().equals(t.getCeritifiCateName())) {
    // if (booleans.get(0)) {
    // num.getAndIncrement();
    // }
    // data.put(entry.getKey(), qualifiedPerFunVO);
    // break;
    // }
    // }
    // data.put("certificateNum", num);
    // });
    //
    // }

    private void initActivityData(Map<String, Object> data, CertificateCardDto dto, Map<String, Object> title) {
        AtomicInteger num = new AtomicInteger(0);
        List<Boolean> booleans;
        for (CeritificateAndEmpDo certificate : dto.getCeritificateList()) {
            if (certificate.isDeleted()) {
                continue;
            }
            QualifiedPerFunVO qualifiedPerFunVO = new QualifiedPerFunVO();
            booleans = checkColorAndUse(certificate);
            qualifiedPerFunVO.setIsUse(booleans.get(0));
            qualifiedPerFunVO.setIsColor(booleans.get(1));
            qualifiedPerFunVO.setValue(certificate.getCeritifiCateName());
            String key = getKeyByValue(title, certificate.getCeritifiCateBid());
            if (key != null) {
                if (booleans.get(0)) {
                    num.getAndIncrement();
                }
                data.put(key, qualifiedPerFunVO);
                data.put("expireDate", certificate.getExpireTime());
            }
        }
        data.put("certificateNum", num);
    }

    private String getKeyByValue(Map<String, Object> map, String value) {
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (entry.getValue().equals(value)) {
                return entry.getKey();
            }
        }
        return null;
    }

    private void initactivit(Map<String, Object> title, String head) {
        CertificateTypeQueryDto typeQueryDto = new CertificateTypeQueryDto();
        typeQueryDto.setStatus("0");
        List<CertificateTypeVo> list = certificateTypeService.getList(typeQueryDto);
        List<String> typeBidList = Sequences.sequence(list)
                .flatMap(CertificateTypeVo::getChildren).map(AbstractData::getBid).toList();
        CertificateQueryDto dto = new CertificateQueryDto();
        dto.setStatus("0");
        String userId = UserContext.getUserId();
        List<CeritificateTopVo> query = empCertifateDo.query(userId);
        List<String> cerBids = new ArrayList<>();
        for (CeritificateTopVo topVo : query) {
            if (topVo.getChildren() == null) {
                continue;
            }
            for (CeritificateTopVo child : topVo.getChildren()) {
                cerBids.add(child.getBid());
            }
        }
        List<CertificateDo> cerDoList;
        if (cerBids.size() > 0) {
            cerDoList = certificateRepository.selectListByIds(cerBids);
        } else {
            // 全部可用证书集合
            cerDoList = certificateDo.selectList(dto);
        }

        cerDoList.sort((cerDo1, cerDo2) -> {
            val type1 = typeBidList.indexOf(cerDo1.getTypeBid());
            val type2 = typeBidList.indexOf(cerDo2.getTypeBid());
            if (type1 == type2) {
                Integer sort1 = Optional.ofNullable(cerDo1.getSortNum()).orElse(0);
                Integer sort2 = Optional.ofNullable(cerDo2.getSortNum()).orElse(0);
                return sort1.compareTo(sort2);
            }
            else {
                return Integer.compare(type1, type2);
            }
        });
        List<String> cerList = cerDoList.stream().map(CertificateDo::getI18nName).distinct()
                .collect(Collectors.toList());
        // if (cerBids.size()>0){
        // cerList =
        // certificateRepository.selectListByIds(cerBids).stream().map(CertificateDo::getI18nName).distinct().collect(Collectors.toList());
        // }else {
        // //全部可用证书集合
        // cerList =
        // certificateDo.selectList(dto).stream().map(CertificateDo::getI18nName).distinct().collect(Collectors.toList());
        // }

        List<Object> valueList = cerList.stream()
                .map(t -> {
                    try {
                        Map map = FastjsonUtil.toObject(t, Map.class);
                        if (map == null) {
                            return null;
                        }
                        return map.get(head == null ? "default" : head) == null ? map.get("default")
                                : map.get(head == null ? "default" : head);
                    } catch (Exception e) {
                        log.error(e.getMessage());
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        int num = 0;
        for (Object t : valueList) {
            num++;
            title.put("certificateName" + (num), t);
        }
    }

    /**
     * 处理数据体
     */
    private void initData(Map<String, Object> title, Map<String, Object> map, String empId, Map empInfoSimple) {
        Map empPrivateInfo = (Map) empInfoSimple.get("empPrivateInfo");
        Map empBasicInfo = (Map) empInfoSimple.get("empBasicInfo");
        Map empWorkInfo = (Map) empInfoSimple.get("empWorkInfo");
        if (!"enabled".equals(postTxtShowCode)) {
            if (empWorkInfo.containsKey("postTxt")) {
                val post = (String) empWorkInfo.get("postTxt");
                if (StringUtils.isNotEmpty(post) && post.indexOf("(") >= 0) {
                    empWorkInfo.put("postTxt", post.substring(0, post.lastIndexOf("(")));
                }
            }
        }
        Map dataMap = new HashMap<>();
        dataMap.putAll(empPrivateInfo);
        dataMap.putAll(empBasicInfo);
        dataMap.putAll(empWorkInfo);
        // if (title!=null){
        // List<Object> collect = title.values().stream().collect(Collectors.toList());
        // for (Object value : collect) {
        // if (map.containsKey(value)){
        // map.put("empId", empInfoEntity.getEmpId());
        // }
        // }
        // }
        map.putAll(dataMap);
        // 多添件一个empid
        map.put("empId", empId);
    }

    private void initEmpTitle(Map<String, Object> title) {
        CertificateSettingDo certificateSettingDo = new CertificateSettingDo();
        certificateSettingDo.setTypeCode("qua");
        Object d = certificateSettingService.selectList(certificateSettingDo);
        List<SettingDto> settingDtos = FastjsonUtil.toArrayList(FastjsonUtil.toJson(d), SettingDto.class);
        title.put("workno", "工号");
        title.put("name", "姓名");
        title.put("organizeTxt", "任职组织");
        title.put("postTxt", "岗位");
        for (SettingDto settingDto : settingDtos) {
            title.put(settingDto.getProperty(), settingDto.getName());
        }

    }

    /**
     * 对颜色或者是否使用进行check
     */
    private List<Boolean> checkColorAndUse(CeritificateAndEmpDo empDo) {
        QualifiedPerQueryDto data = new QualifiedPerQueryDto();
        data.setCeritifiCateName(empDo.getCeritifiCateName());
        data.setStatus("0");
        List<Boolean> booleans = Lists.list();
        Boolean isUse = true; // true打勾 false不打勾

        if (empDo.getExpireTime() != null) {
            String time = StringUtils.rightPad(empDo.getExpireTime(), 13, "0");
            Date date = new Date(Long.parseLong(time));
            Date nowData = new Date(System.currentTimeMillis());
            if (date.before(nowData)) {
                isUse = false;
            }
        }
        // 限制使用开关 开启
        if (empDo.getIsuse().getValue().equals("0")) {
            isUse = false;
        }
        booleans.add(isUse);
        // 项目登记使用 false是不填充
        Boolean isColor = false;
        if (StringUtils.isNotEmpty(empDo.getProBid())) {
            isColor = true;
        }
        booleans.add(isColor);
        return booleans;
    }

    public void addQualifiedPerVo(List<QualifiedPerVo> list, String filaCode, String fileName, String value,
            Boolean IsColor, Boolean IsUse) {
        list.add(new QualifiedPerVo(filaCode, fileName, value, IsColor, IsUse));
    }

    // 设置证书头
    public String saveSetTop(SetTopSaveDto dto) {
        // 存在性验证
        List<EmpCertificateDo> query = SpringUtil.getBean(IEmpCertificateRepository.class).query(dto.getEmpId());
        if (CollectionUtils.isNotEmpty(query)) {
            empCertifateDo.remove(query.get(0).getBid());
        }
        List<CeritificateTopVo> certificateNames = dto.getCertificateNames();
        String cerBid = "";
        for (CeritificateTopVo certificateName : certificateNames) {

            for (CeritificateTopVo child : certificateName.getChildren()) {
                if (child.isChecked()) {
                    cerBid = child.getBid() + "," + cerBid;
                }

            }
        }
        cerBid = cerBid.replaceAll(",+$", "");
        dto.setJson(cerBid);
        return empCertifateDo.save(dto);
    }

    // 展示可用的证书
    public List<CeritificateTopVo> selectUseCertificate() {
        // 1. 查询所有证书类型
        CertificateTypeQueryDto typeQueryDto = new CertificateTypeQueryDto();
        typeQueryDto.setStatus("0");
        List<CertificateTypeVo> list = certificateTypeService.getList(typeQueryDto);
        List<CertificateTypeVo> typeVoList = Sequences.sequence(list)
                .flatMap(CertificateTypeVo::getChildren).toList();


        // 2. 查询所有证书
        CertificateQueryDto certificateDto = new CertificateQueryDto();
        certificateDto.setStatus("0");
        certificateDto.setPageNo(1);
        certificateDto.setPageSize(-1);
        List<CertificateDo> certificates = certificateDo.pageList(certificateDto).getItems();

        // 3. 处理数据并返回结果
        return processAndSortCertificates(typeVoList, certificates);
    }

    /**
     * 处理证书类型和证书数据，并按排序规则返回结果
     * 
     * @param allTypeList  所有证书类型列表
     * @param certificates 所有证书列表
     * @return 排序后的证书类型VO列表
     */
    private List<CeritificateTopVo> processAndSortCertificates(List<CertificateTypeVo> allTypeList,
            List<CertificateDo> certificates) {

        // 按证书类型分组
        Map<String, List<CertificateDo>> certificatesByType = groupCertificatesByType(certificates);

        // 创建并填充topVo列表
        List<CeritificateTopVo> topVos = createTopVosFromLevel2Types(allTypeList, certificatesByType);

        // 按pSort和sortNum排序返回结果
        return sortTopVosByPSortAndSortNum(topVos);
    }

    /**
     * 创建level1类型的映射
     * 
     * @param level1Types level为1的证书类型列表
     * @return 映射表
     */
    private Map<String, CertificateTypeDo> createLevel1TypeMap(List<CertificateTypeDo> level1Types) {
        return level1Types.stream()
                .collect(Collectors.toMap(CertificateTypeDo::getBid, type -> type));
    }

    /**
     * 按证书类型分组
     * 
     * @param certificates 证书列表
     * @return 分组后的映射表
     */
    private Map<String, List<CertificateDo>> groupCertificatesByType(List<CertificateDo> certificates) {
        return certificates.stream()
                .collect(Collectors.groupingBy(CertificateDo::getTypeBid));
    }

    /**
     * 从level2类型创建topVo列表
     * 
     * @param level2Types        level为2的证书类型列表
     * @param certificatesByType 按类型分组的证书映射表
     * @return topVo列表
     */
    private List<CeritificateTopVo> createTopVosFromLevel2Types(
            List<CertificateTypeVo> level2Types,
            Map<String, List<CertificateDo>> certificatesByType) {

        List<CeritificateTopVo> topVos = new ArrayList<>();

        for (CertificateTypeVo typeDo : level2Types) {
            CeritificateTopVo topTypeVo = convertTypeToTopVo(typeDo);

            // 设置pSort - 从level1类型获取sortNum
            // findPSortFromParentType(topTypeVo, typeDo, level1TypeMap);

            // 添加子证书列表
            addCertificatesToTopVo(topTypeVo, topTypeVo.getBid(), certificatesByType);

            topVos.add(topTypeVo);
        }

        return topVos;
    }

    /**
     * 将证书类型转换为TopVo
     * 
     * @param typeDo 证书类型
     * @return TopVo对象
     */
    private CeritificateTopVo convertTypeToTopVo(CertificateTypeVo typeDo) {
        CeritificateTopVo topTypeVo = new CeritificateTopVo();
        Map i18nName = typeDo.getI18nName();
        topTypeVo.setName(LangUtil.getCurrentLangVal(i18nName));
        topTypeVo.setBid(typeDo.getBid());
        topTypeVo.setChecked(true);
        topTypeVo.setUpdateTime(typeDo.getUpdateTime());
        topTypeVo.setSortNum(null == typeDo.getSortNum() ? 0 : typeDo.getSortNum());
        return topTypeVo;
    }

    /**
     * 从父类型设置pSort
     * 
     * @param topTypeVo     TopVo对象
     * @param typeDo        证书类型
     * @param level1TypeMap level1类型映射表
     */
    private void findPSortFromParentType(
            CeritificateTopVo topTypeVo,
            CertificateTypeDo typeDo,
            Map<String, CertificateTypeDo> level1TypeMap) {

        String pBid = typeDo.getPBid();
        if (pBid != null && level1TypeMap.containsKey(pBid)) {
            CertificateTypeDo parentType = level1TypeMap.get(pBid);
            topTypeVo.setPSort(null == parentType.getSortNum() ? 0 : parentType.getSortNum());
        } else {
            topTypeVo.setPSort(0); // 如果找不到父类型，默认为0
        }
    }

    /**
     * 添加证书到TopVo
     * 
     * @param topTypeVo          TopVo对象
     * @param typeBid            类型ID
     * @param certificatesByType 按类型分组的证书映射表
     */
    private void addCertificatesToTopVo(
            CeritificateTopVo topTypeVo,
            String typeBid,
            Map<String, List<CertificateDo>> certificatesByType) {

        // 获取该类型下的所有证书
        List<CertificateDo> typeCertificates = certificatesByType.getOrDefault(typeBid, new ArrayList<>());

        // 组装子证书列表
        List<CeritificateTopVo> topCerVos = new ArrayList<>();
        for (CertificateDo aDo : typeCertificates) {
            CeritificateTopVo topCerVo = new CeritificateTopVo();
            topCerVo.setName(LangUtil.getCurrentLangVal(FastjsonUtil.toObject(aDo.getI18nName(), Map.class)));
            topCerVo.setBid(aDo.getBid());
            topCerVo.setChecked(true);
            topCerVo.setSortNum(null == aDo.getSortNum() ? 0 : aDo.getSortNum());
            topCerVo.setPSort(topTypeVo.getPSort()); // 子证书的pSort等于父类型的pSort
            topCerVos.add(topCerVo);
        }

        // 按sortNum排序子证书
        topTypeVo.setChildren(topCerVos.stream()
                .sorted(Comparator.comparingInt(CeritificateTopVo::getSortNum))
                .collect(Collectors.toList()));
    }

    /**
     * 按pSort和sortNum排序TopVo列表
     * 
     * @param topVos TopVo列表
     * @return 排序后的列表
     */
    private List<CeritificateTopVo> sortTopVosByPSortAndSortNum(List<CeritificateTopVo> topVos) {
        return topVos.stream()
                .filter(type->!type.getChildren().isEmpty())
                // .sorted(Comparator.comparingInt(CeritificateTopVo::getSortNum))
                .collect(Collectors.toList());
    }

    /**
     * 导出
     */
    public void export(CeritificateAndEmpQueryDto dto, String header, HttpServletResponse response) {
        // 数据
        dto.setPageSize(5000);
        List<Map<String, Object>> items = pageList(dto, header).getItems();

        List<ExcelExportEntity> colList = new ArrayList<>();
        for (TagProperty tagProperty : exportHeader(items)) {
            ExcelExportEntity exprortEntity = new ExcelExportEntity(tagProperty.getPropertyTxt(),
                    tagProperty.getProperty(), 15);
            exprortEntity.setOrderNum(tagProperty.getOrder());
            colList.add(exprortEntity);
        }
        List<Map<String, Object>> maps = initDataList(items, 1);
        List<Map<String, Object>> mapList = initDataList(items, 2);
        try {
            ExcelUtils.downloadDataListCustom(colList, maps, mapList, "资格人员一览", response);
        } catch (Exception e) {
            log.error("download approval List excel err.{}", e.getMessage(), e);
        }
    }

    private List<Map<String, Object>> initDataList(List<Map<String, Object>> dataList, int type) {
        List<Map<String, Object>> list = Lists.list();
        if (type == 1) {
            dataList.forEach(t -> {
                Map<String, Object> map = new HashMap<>();
                int cou = 0;
                for (Map.Entry<String, Object> qualifiedPerVo : t.entrySet()) {
                    if (qualifiedPerVo.getKey().contains("certificateName")) {
                        cou++;
                        map.put("证书" + cou, qualifiedPerVo.getValue());
                        map.remove(qualifiedPerVo.getKey());

                    }

                }
                list.add(map);
            });
            return list;
        } else {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            dataList.forEach(t -> {
                int cou = 0;
                for (Map.Entry<String, Object> qualifiedPerVo : t.entrySet()) {
                    if (qualifiedPerVo.getKey().contains("certificateName")) {
                        cou++;
                        t.put(qualifiedPerVo.getKey(), "证书" + cou);
                    }
                    if (qualifiedPerVo.getKey().equals("hireDate") && qualifiedPerVo.getValue() != null) {
                        t.put("hireDate", sdf.format(new Date((Long) qualifiedPerVo.getValue())));
                    }
                }
                t.putAll((Map) t.get("ext"));
                list.add(t);
            });
            return list;
        }

    }

    public List<TagProperty> exportHeader(List<Map<String, Object>> titlelist) {
        List<TagProperty> list = new ArrayList<>();
        Map<String, Object> empMap = (Map<String, Object>) titlelist.get(0).get("empTitle");
        Map<String, Object> map = (Map<String, Object>) titlelist.get(0).get("title");
        int order = 0;
        for (Map.Entry<String, Object> entry : empMap.entrySet()) {
            addTagPropertyToList(list, entry.getKey()
                    .contains("@") ? StringUtils.substringAfterLast(entry.getKey(), "@") : entry.getKey(), entry.getValue()
                    .toString(), ++order);
        }
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            addTagPropertyToList(list, entry.getKey(), entry.getValue().toString(), ++order);
        }

        return list;
    }

    public void addTagPropertyToList(List<TagProperty> list, String property, String propertyTxt, int order) {
        list.add(new TagProperty(property, propertyTxt, order));
    }

    public Object getTopDatails(String userId) {
        return empCertifateDo.query(userId);
    }
}
