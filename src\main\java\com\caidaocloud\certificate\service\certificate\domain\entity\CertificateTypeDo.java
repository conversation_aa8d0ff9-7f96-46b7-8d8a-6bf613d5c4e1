package com.caidaocloud.certificate.service.certificate.domain.entity;

import com.caidaocloud.certificate.service.certificate.domain.base.entity.DataEntity;
import com.caidaocloud.certificate.service.certificate.domain.base.enums.BusinessEventTypeEnum;
import com.caidaocloud.certificate.service.certificate.domain.base.util.UserContext;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICertificateTypeRepository;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateTypeQueryDto;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import groovy.util.logging.Slf4j;
import lombok.Data;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/9 17:33
 **/
@Data
@Slf4j
@Service
public class CertificateTypeDo extends DataEntity {
    public static final String IDENTIFIER = "entity.certificate.certificateType";

    /**
     * 名称
     */
    private String name;
    /**
     * 编码
     */
    private String code;
    /**
     * 层级
     */
    private Integer level;
    /**
     * 序列编码
     */
    private Integer sortNum;
    /**
     * 状态
     */
    private EnumSimple status;
    /**
     * 上级职务类型
     */
    private String pBid;
    /**
     * 证书名称类型多语言
     */
    private String i18nName;

    @Resource
    private ICertificateTypeRepository CertificateTypeRepository;


    /**
     * 保存
     * @param data
     */
    public String save(CertificateTypeDo data) {
        UserInfo userInfo = UserContext.preCheckUser();
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
        DataEntity.initFieldValue(IDENTIFIER, BusinessEventTypeEnum.CREATE, data, null);
        data.setCreateBy(userId);
        data.setCreateTime(System.currentTimeMillis());
        data.setUpdateBy(userId);
        data.setUpdateTime(data.getCreateTime());
        data.setBid(SnowUtil.nextId());
        String dataId = DataInsert.identifier(data.getIdentifier()).insert(data);
        return dataId;
    }

    /**
     * 修改
     * @param data
     */
    public void update(CertificateTypeDo data) {
        UserInfo userInfo = UserContext.preCheckUser();
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
        CertificateTypeDo dbData = selectById(data.getBid());
        data.setUpdateBy(userId);
        data.setUpdateTime(data.getCreateTime());
        DataEntity.initFieldValue(IDENTIFIER, BusinessEventTypeEnum.UPDATE, data, dbData);
        CertificateTypeRepository.updateById(data);
    }

    /**
     * 查看详情
     * @param bid
     * @return
     */
    public CertificateTypeDo selectById(String bid) {
        return CertificateTypeRepository.selectById(bid, IDENTIFIER);
    }
    /**
     * 删除
     *
     * @param data
     */
    public void delete(CertificateTypeDo data) {
        data.setIdentifier(IDENTIFIER);
        CertificateTypeRepository.delete(data);
    }

    /**
     * 列表查询
     * @param dto
     * @return
     */
    public List<CertificateTypeDo> selectList(CertificateTypeQueryDto dto) {
        List<CertificateTypeDo> list = CertificateTypeRepository.selectList(dto, IDENTIFIER);
        list = list.stream().sorted((a,b)->{
            Integer sa = Optional.ofNullable(a.getSortNum()).orElse(0);
            Integer sb = Optional.ofNullable(b.getSortNum()).orElse(0);
            if (sa.equals(sb)) {
                return Long.valueOf(b.getBid()).compareTo(Long.valueOf(a.getBid()));
            }
            else {
                return sa.compareTo(sb);
            }
        }).collect(Collectors.toList());
        return list;
    }

    public List<CertificateTypeDo> all(CertificateTypeQueryDto dto) {
        List<CertificateTypeDo> list = CertificateTypeRepository.all();
        list = list.stream().sorted(Comparator.comparing(CertificateTypeDo::getUpdateTime).reversed()).collect(Collectors.toList());
        return list;
    }

    public void updateStatus(CertificateTypeDo data, BusinessEventTypeEnum enable) {
        CertificateTypeDo dbData = selectById(data.getBid());
        check(dbData);
        DataEntity.initFieldValue(IDENTIFIER, enable, data, dbData);
        CertificateTypeRepository.updateById(data);
    }

    private void check(CertificateTypeDo data) {
        PreCheck.preCheckArgument(null == data || null == data.getBid(), "证书类型不存在");
    }
}
