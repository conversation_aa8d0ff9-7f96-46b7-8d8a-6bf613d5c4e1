package com.caidaocloud.certificate.service.certificate.infrastructure.respository.impl;

import com.caidaocloud.certificate.service.certificate.application.dto.CertificateCardDto;
import com.caidaocloud.certificate.service.certificate.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.certificate.service.certificate.domain.base.util.UserContext;
import com.caidaocloud.certificate.service.certificate.domain.entity.CeritificateAndEmpDo;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateDo;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICeritificateAndEmpRepository;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CeritificateAndEmpQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.QualifiedPerQueryDto;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.dto.AbstractData;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Address;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataJoin;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.util.ObjectUtil;
import com.caidaocloud.util.StringUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequences;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.lucene.search.FieldComparator;

import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

import static com.caidaocloud.certificate.service.certificate.domain.entity.CeritificateAndEmpHistoryDo.convertToStartOfDayTimestamp;

/**
 * 人员证书管理
 */
@Repository
public class CeritificateAndEmpRepositoryImpl extends BaseRepositoryImpl<CeritificateAndEmpDo> implements ICeritificateAndEmpRepository {
    @Override
    public List<CeritificateAndEmpDo> queryList(CeritificateAndEmpQueryDto data) {
        return queryPage(data, 5000, 1).getItems();
    }

    @Override
    public PageResult<CeritificateAndEmpDo> queryPage(CeritificateAndEmpQueryDto data,int pageSize,int pageNo) {
        DataFilter filter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .andNe("deleted", Boolean.TRUE.toString());
        //注册时间
        if (StringUtil.isNotEmpty(data.getRegisterTime())) {
            String timestamp = convertToStartOfDayTimestamp(Long.valueOf(data.getRegisterTime()));
            String startTime=StringUtils.rightPad(timestamp,13,"0");
            Long end = Long.valueOf(startTime) + 86400000;
            String endTime=String.valueOf(end);
            filter = filter.and(DataFilter.ge("registerTime", startTime).andLe("registerTime", endTime));
        }

        //签发地
        if (StringUtil.isNotEmpty(data.getIssueAt())) {
            filter = filter.and(DataFilter.eq("issueAt$dict$value", data.getIssueAt()));
        }
        //员工id
        if (StringUtil.isNotEmpty(data.getEmpId())) {
            filter = filter.and(DataFilter.eq("empId", data.getEmpId()));
        }
        //入省备案
        if (StringUtil.isNotEmpty(data.getAccessProvince())) {
            filter = filter.and(DataFilter.eq("accessProvince$dict$value", data.getAccessProvince()));
        }

        if (StringUtil.isNotEmpty(data.getTypeName())) {
            filter = filter.and(DataFilter.eq("typeName", data.getTypeName()));
        }
        if (StringUtil.isNotEmpty(data.getTypeBid())) {
            filter = filter.and(DataFilter.eq("typeBid", data.getTypeBid()));
        }
        //证书子类
        if (StringUtil.isNotEmpty(data.getTypeSubName())) {
            filter = filter.and(DataFilter.eq("typeSubName", data.getTypeSubName()));
        }
        if (StringUtil.isNotEmpty(data.getTypeSubBid())) {
            filter = filter.and(DataFilter.eq("typeSubBid", data.getTypeSubBid()));
        }
        //证书
        if (StringUtil.isNotEmpty(data.getCeritifiCateName())) {
            filter = filter.and(DataFilter.eq("ceritifiCateName", data.getCeritifiCateName()));
        }
        //证书bid
        if (StringUtil.isNotEmpty(data.getCeritifiCateBid())) {
            filter = filter.and(DataFilter.eq("ceritifiCateBid", data.getCeritifiCateBid()));
        }
        //empids
        if (StringUtil.isNotEmpty(data.getEmpIds())) {
            filter = filter.and(DataFilter.in("empId", data.getEmpIds()));
        }

        filter = (DataFilter) data.doDataFilter(data.getFilters(), filter);
        return DataQuery.identifier(CeritificateAndEmpDo.IDENTIFIER)
                .decrypt().specifyLanguage().queryInvisible()
                .limit(pageSize, pageNo)
                .filter(filter, CeritificateAndEmpDo.class);
    }

    @Override
    public PageResult<CertificateCardDto> queryCardPage(CeritificateAndEmpQueryDto data, int pageSize, int pageNo, boolean filterAvailable) {

        PageResult<CeritificateAndEmpDo> allCertificate = basicJoinPage(data, -1, 1);
        List<CeritificateAndEmpDo> items = allCertificate.getItems();
        long total = Sequences.sequence(items).map(CeritificateAndEmpDo::getEmpId).stream()
                .distinct().count();
        List<String> cardEmpIds = Sequences.sequence(items).map(CeritificateAndEmpDo::getEmpId)
                .stream().distinct().collect(Collectors.toList());
        if((pageNo-1)*pageSize>=total){
            return new PageResult<>(Lists.list(), pageSize, pageNo, 0);
        }else{
            cardEmpIds=cardEmpIds.subList((pageNo-1)*pageSize,pageSize*pageNo>total?(int)total:pageNo*pageSize);
        }
        if (total<=0) {
            return new PageResult<>(Lists.list(), pageSize, pageNo, 0);
        }
        List<CeritificateAndEmpDo> list = DataQuery.identifier(CeritificateAndEmpDo.IDENTIFIER)
                .decrypt().specifyLanguage().queryInvisible()
                .limit(-1, 1)
                .filter(DataFilter.in("empId", cardEmpIds).andNe("deleted", Boolean.TRUE.toString()), CeritificateAndEmpDo.class).getItems();
        Map<String, List<CeritificateAndEmpDo>> map = Sequences.sequence(list)
                .toMap(CeritificateAndEmpDo::getEmpId);
        List<String> finalCardEmpIds = cardEmpIds;
        List<CertificateCardDto> cardList = map.entrySet().stream()
                .map(entry -> new CertificateCardDto(entry.getKey(), entry.getValue()))
                .sorted(Comparator.comparing(e -> finalCardEmpIds.indexOf(e.getEmpId())))
                .collect(Collectors.toList());
        return new PageResult(cardList, pageNo, pageSize, (int) total);
    }

    @Override
    public PageResult<CeritificateAndEmpDo> queryTablePage(CeritificateAndEmpQueryDto data, int pageSize, int pageNo) {
        PageResult<CeritificateAndEmpDo> allCertificate = basicJoinPage(data, pageSize, pageNo);
        List<String> bids = Sequences.sequence(allCertificate.getItems()).map(CeritificateAndEmpDo::getBid).toList();
        if (CollectionUtils.isEmpty(bids)){
            return new PageResult<>(new ArrayList<>(), pageNo, pageSize, 0);
        }
        List<CeritificateAndEmpDo> list = DataQuery.identifier(CeritificateAndEmpDo.IDENTIFIER)
                .decrypt().specifyLanguage().queryInvisible()
                .limit(bids.size(), 1)
                .filter(DataFilter.in("bid", bids), CeritificateAndEmpDo.class).getItems();
        list = list.stream()
                .sorted(Comparator.comparing(e -> bids.indexOf(e.getBid())))
                .collect(Collectors.toList());
        return new PageResult<>(list, pageNo, pageSize, allCertificate.getTotal());
    }

    public PageResult<CeritificateAndEmpDo> basicJoinPage(CeritificateAndEmpQueryDto data, int pageSize, int pageNo) {
        DataJoin.ModelInfo workInfoModel = workInfoModel(data);
        DataJoin.ModelInfo workOverviewModel = workOverviewModel(data);
        DataJoin.ModelInfo certificateModel = activeCertificateModel(data);
        DataJoin join = DataJoin.joinModels(certificateModel,workInfoModel,
                DataJoin.JoinInfo.joinInfo(Lists.list(DataJoin.JoinPropertyInfo.joinProperty(certificateModel.getIdentifier(), workInfoModel.getIdentifier(), "empId", "empId")))
        );
        if (data.getWorkAge() != null) {
            join.joinModel(workOverviewModel,
                    DataJoin.JoinInfo.joinInfo(Lists.list(DataJoin.JoinPropertyInfo.joinProperty( certificateModel.getIdentifier(),workOverviewModel.getIdentifier(), "empId", "empId"))));

        }
       join.limit(pageSize, pageNo);
        PageResult<Triple<CeritificateAndEmpDo, CeritificateAndEmpDo, CeritificateAndEmpDo>> result = join.join(CeritificateAndEmpDo.class, System.currentTimeMillis());
        return new PageResult<>(Sequences.sequence(result.getItems()).map(Triple::getLeft)
                .toList(), result.getPageNo(), result.getPageSize(), result.getTotal());
    }

    private DataJoin.ModelInfo workInfoModel(CeritificateAndEmpQueryDto data){
        String socialSecurity = Optional.ofNullable(data
                .getSocialSecurity()).map(Address::doValue).orElse(null);
        DataFilter filter = DataFilter.ne("deleted", Boolean.TRUE.toString())
//                .andEqIf("organize", data.getOrganize(), () -> StringUtils.isNotEmpty(data.getOrganize()))
                .andInIf("organize", Lists.list(StringUtils.trimToEmpty(data.getOrganize()).split(",")), ()->StringUtils.isNotBlank(data.getOrganize()))
//                .andEqIf("post", data.getPost(), () -> StringUtils.isNotEmpty(data.getPost()))
                .andInIf("post", Lists.list(StringUtils.trimToEmpty(data.getPost()).split(",")), ()->StringUtils.isNotBlank(data.getPost()))
//                .andEqIf("empStatus", data.getEmpStatus(), () -> StringUtils.isNotEmpty(data.getEmpStatus()))
                .andInIf("empStatus", Lists.list(StringUtils.trimToEmpty(data.getEmpStatus()).split(",")), ()->StringUtils.isNotBlank(data.getEmpStatus()))
//                .andEqIf("job", data.getJob(), () -> StringUtils.isNotEmpty(data.getJob()))
                .andInIf("job", Lists.list(StringUtils.trimToEmpty(data.getJob()).split(",")), ()->StringUtils.isNotBlank(data.getJob()))
//                .andEqIf("jobGrade$startGrade", data.getJobLevel(), () -> StringUtils.isNotEmpty(data.getJobLevel()))
                .andInIf("jobGrade$startGrade", Lists.list(StringUtils.trimToEmpty(data.getJobLevel()).split(",")), ()->StringUtils.isNotBlank(data.getJobLevel()))
                .andEqIf("socialSecurity", socialSecurity, () -> socialSecurity != null)
                .andEqIf("shebaodi", data.getShebaodi(), ()->StringUtils.isNotEmpty(data.getShebaodi()));
        if (StringUtils.isNotEmpty(data.getBenchPost())) {
            List<DataSimple> postList = DataQuery.identifier("entity.hr.Post").limit(-1, 1)
                    .filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
//                            .andEq("benchmarkPositionId", data.getBenchPost()), DataSimple.class).getItems();
                            .andIn("benchmarkPositionId", Lists.list(StringUtils.trimToEmpty(data.getBenchPost()).split(","))), DataSimple.class).getItems();
            if (postList.isEmpty()) {
                filter = filter.andEq("tenantId", "none");
            }
            else {
                filter = filter.andIn("post", Sequences.sequence(postList).map(AbstractData::getBid).toList());
            }
        }
        if (StringUtils.isNotEmpty(data.getNameOrNo())){
            filter=filter.and(DataFilter.regex("workno", data.getNameOrNo()).orRegex("name", data.getNameOrNo()));
        }
        return DataJoin.ModelInfo.model("entity.hr.EmpWorkInfo", Lists.list(),filter).orderBy("workno");
    }

    private DataJoin.ModelInfo workOverviewModel(CeritificateAndEmpQueryDto data){
        return DataJoin.ModelInfo.model("entity.hr.EmpWorkOverview", Lists.list(),
                DataFilter.ne("deleted", Boolean.TRUE.toString())
                        .andEqIf("workAge", String.valueOf(data.getWorkAge()), () -> data.getWorkAge() != null));
    }

    private DataJoin.ModelInfo activeCertificateModel(CeritificateAndEmpQueryDto data){
        DataFilter filter = DataFilter.ne("deleted", Boolean.TRUE.toString())
//                .andEqIf("typeBid", data.getTypeBid(), () -> StringUtils.isNotEmpty(data.getTypeBid()))
                .andInIf("typeBid", Lists.list(StringUtils.trimToEmpty(data.getTypeBid()).split(",")), ()->StringUtils.isNotEmpty(data.getTypeBid()))
//                .andEqIf("typeSubBid", data.getTypeSubBid(), () -> StringUtils.isNotEmpty(data.getTypeSubBid()))
                .andInIf("typeSubBid", Lists.list(StringUtils.trimToEmpty(data.getTypeSubBid()).split(",")), ()->StringUtils.isNotEmpty(data.getTypeSubBid()))
                .andEqIf("ceritifiCateBid", data.getCeritifiCateBid(), () -> StringUtils.isNotEmpty(data.getCeritifiCateBid()))
                .andInIf("ceritifiCateBid", data.getBids(), () -> CollectionUtils.isNotEmpty(data.getBids()))
                // .andRegexIf("ceritifiCateName", data.getCeritifiCateName(), () -> StringUtils.isNotEmpty(data.getCeritifiCateName()))
//                .andEqIf("issueAt$dict$value", data.getIssueAt(), () -> StringUtils.isNotEmpty(data.getIssueAt()))
                .andInIf("issueAt$dict$value", Lists.list(StringUtils.trimToEmpty(data.getIssueAt()).split(",")), ()->StringUtils.isNotBlank(data.getIssueAt()))
//                .andEqIf("isRegister", data.getIsRegister(), () -> StringUtils.isNotEmpty(data.getIsRegister()))
                .andInIf("isRegister", Lists.list(StringUtils.trimToEmpty(data.getIsRegister()).split(",")), ()->StringUtils.isNotBlank(data.getIsRegister()))
                .andEqIf("certifiCateNums", String.valueOf(data.getCertifiCateNums()), () -> ObjectUtil.isNotEmpty(data.getCertifiCateNums()))
                .andInIf("ceritifiCateBid", Lists.list(StringUtils.trimToEmpty(data.getCertificateIds()).split(",")), ()->StringUtils.isNotEmpty(data.getCertificateIds()))
                .andEqIf("approveStatus", data.getCeritificateStatus(), () -> StringUtils.isNotEmpty(data.getCeritificateStatus()))
                .andEqIf("useStatus", data.getUseStatus(), () -> StringUtils.isNotEmpty(data.getUseStatus()))
//                .andEqIf("accessProvince$dict$value", data.getAccessProvince(), (() -> StringUtils.isNotEmpty(data.getAccessProvince())));
                .andInIf("accessProvince$dict$value", Lists.list(StringUtils.trimToEmpty(data.getAccessProvince()).split(",")), ()->StringUtils.isNotBlank(data.getAccessProvince()));
        if (StringUtils.isNotEmpty(data.getExpireTime())) {
            filter=filter.and(DataFilter.le("expireTime", data.getExpireTime()));
        }else if (StringUtils.isNotEmpty(data.getApiType())){
            filter=filter.and(DataFilter.eq("expireTime", null)
                    .or(DataFilter.ge("expireTime", String.valueOf(System.currentTimeMillis()))));
        }
        if (StringUtils.isNotEmpty(data.getCeritifiCateName())) {
            List<CertificateDo> certificateDoList = DataQuery.identifier(CertificateDo.IDENTIFIER)
                    .limit(-1, 1)
                    .filter(DataFilter.regex("name", data.getCeritifiCateName()), CertificateDo.class).getItems();
            if (certificateDoList.isEmpty()) {
                filter = filter.andEq("tenantId", "none");
            }
            else {
                filter = filter.andIn("ceritifiCateBid", Sequences.sequence(certificateDoList).map(AbstractData::getBid)
                        .toList());
            }
        }
        return DataJoin.ModelInfo.model(CeritificateAndEmpDo.IDENTIFIER, Lists.list("bid","emp_id"),filter
        );
    }

    @Override
    public List<CeritificateAndEmpDo> select(QualifiedPerQueryDto data) {
        DataFilter filter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .andNe("deleted", Boolean.TRUE.toString());

        //员工id
        if (StringUtil.isNotEmpty(data.getEmpId())) {
            filter = filter.and(DataFilter.eq("empId", data.getEmpId()));
        }
        //证书
        if (StringUtil.isNotEmpty(data.getCeritifiCateName())) {
            filter = filter.and(DataFilter.eq("ceritifiCateName", data.getCeritifiCateName()));
        }
//        //状态
//        if (StringUtil.isNotEmpty(data.getCeritifiCateName())) {
//            filter = filter.and(DataFilter.eq("status", data.getStatus()));
//        }
        //empids
        if (StringUtil.isNotEmpty(data.getEmpIds())) {
            filter = filter.and(DataFilter.in("empId", data.getEmpIds()));
        }

        return DataQuery.identifier(CeritificateAndEmpDo.IDENTIFIER)
                .decrypt().specifyLanguage().queryInvisible()
                .limit(5000, 1).
                filter(filter, CeritificateAndEmpDo.class).getItems();
    }

    @Override
    public List<CeritificateAndEmpDo> listByEmpId(String empId, String certificate) {
        DataFilter filter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .andNe("deleted", Boolean.TRUE.toString())
                .andEq("empId", empId)
                .andEqIf("ceritifiCateBid", certificate, () -> StringUtils.isNotEmpty(certificate));
        return DataQuery.identifier(CeritificateAndEmpDo.IDENTIFIER).decrypt().queryInvisible().specifyLanguage()
                .limit(-1, 1)
                .filter(filter, CeritificateAndEmpDo.class).getItems();
    }

    @Override
    public List<CeritificateAndEmpDo> selectBatchIds(List<String> bid) {
        return super.selectBatchIds(bid, CeritificateAndEmpDo.IDENTIFIER);
    }

}
