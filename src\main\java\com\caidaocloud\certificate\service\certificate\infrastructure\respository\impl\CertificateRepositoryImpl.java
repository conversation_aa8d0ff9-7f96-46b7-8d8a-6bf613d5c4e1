package com.caidaocloud.certificate.service.certificate.infrastructure.respository.impl;

import com.caidaocloud.certificate.service.certificate.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.certificate.service.certificate.domain.base.util.UserContext;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateDo;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICertificateRepository;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateQueryDto;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/10 13:47
 * 证书
 **/
@Repository
public class CertificateRepositoryImpl extends BaseRepositoryImpl<CertificateDo> implements ICertificateRepository {


    @Override
    public List<CertificateDo> selectList(CertificateQueryDto basePage, String identifier) {
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .eq("deleted", Boolean.FALSE.toString());
        if (null != basePage.getStatus()) {
            dataFilter = dataFilter.andEq("status", basePage.getStatus());
        }

        if (StringUtil.isNotEmpty(basePage.getNameOrCode())) {
            dataFilter = dataFilter.and(DataFilter.regex("name", basePage.getNameOrCode())
                    .orRegex("code",basePage.getNameOrCode())
            );
        }
        if (StringUtil.isNotEmpty(basePage.getTypeBid())) {
            dataFilter = dataFilter.and(DataFilter.eq("typeBid", basePage.getTypeBid())

            );
        }
        if (StringUtil.isNotEmpty(basePage.getEmpId())) {
            dataFilter = dataFilter.and(DataFilter.eq("empId", basePage.getEmpId())

            );
        }
        return DataQuery.identifier(identifier)
                .decrypt().specifyLanguage().queryInvisible().limit(5000, 1)
                .filter(dataFilter, CertificateDo.class, "sortNum asc,id desc", System.currentTimeMillis()).getItems();
    }

    @Override
    public PageResult<CertificateDo> pageList(CertificateQueryDto queryDto, String identifier) {
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .eq("deleted", Boolean.FALSE.toString());
        if (null != queryDto.getStatus()) {
            dataFilter = dataFilter.andEq("status", queryDto.getStatus());
        }

        if (StringUtil.isNotEmpty(queryDto.getNameOrCode())) {
            dataFilter = dataFilter.and(DataFilter.regex("name", queryDto.getNameOrCode())
                    .orRegex("code",queryDto.getNameOrCode())
            );
        }
        if (StringUtil.isNotEmpty(queryDto.getTypeBid())) {
            dataFilter = dataFilter.and(DataFilter.eq("typeBid", queryDto.getTypeBid())

            );
        }
        return DataQuery.identifier(identifier)
                .decrypt().specifyLanguage().queryInvisible().limit(queryDto.getPageSize(), queryDto.getPageNo())
                .filter(dataFilter, CertificateDo.class, "sortNum asc,id desc", System.currentTimeMillis());



    }

    @Override
    public List<CertificateDo> selectListByIds(List<String> bids) {
        return DataQuery.identifier("entity.certificate.certificate")
                .decrypt().specifyLanguage().queryInvisible()
                .limit(bids.size(), 1)
                .filter(DataFilter.in("bid", bids), CertificateDo.class,"sortNum asc,id desc",System.currentTimeMillis()).getItems();
    }

    @Override
    public List<CertificateDo> all() {
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId());
        return DataQuery.identifier(CertificateDo.IDENTIFIER)
                .decrypt().specifyLanguage().queryInvisible().limit(5000, 1)
                .filter(dataFilter, CertificateDo.class, "sortNum asc,id desc", System.currentTimeMillis()).getItems();
    }

}
