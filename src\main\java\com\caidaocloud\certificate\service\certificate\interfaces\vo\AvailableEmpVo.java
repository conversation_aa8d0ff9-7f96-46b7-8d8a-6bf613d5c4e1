package com.caidaocloud.certificate.service.certificate.interfaces.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 拥有指定证书且可使用的员工VO
 * 
 * <AUTHOR> Assistant
 * @date 2024/12/19
 */
@Data
@ApiModel(description = "拥有指定证书且可使用的员工VO")
public class AvailableEmpVo {
    
    @ApiModelProperty("员工信息")
    private EmpSimple emp;
    
    @ApiModelProperty("证书人员关系ID")
    private String empCertificateBid;
    
    @ApiModelProperty("证书名称")
    private String certificateName;
    
    @ApiModelProperty("证书编码")
    private String certificateCode;
    
    @ApiModelProperty("证书状态（有效/无效）")
    private EnumSimple certificateStatus;
    
    @ApiModelProperty("证书使用状态（可使用/使用中）")
    private EnumSimple useStatus;
    
    @ApiModelProperty("取得日期")
    private Long acquiredTime;
    
    @ApiModelProperty("失效日期")
    private String expireTime;
    
    @ApiModelProperty("注册号")
    private String registrationNo;
    
    @ApiModelProperty("专业")
    private String specialty;
}
