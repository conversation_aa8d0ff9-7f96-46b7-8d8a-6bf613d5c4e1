package com.caidaocloud.certificate.service.certificate.interfaces.facade;

import java.io.OutputStream;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.caidaocloud.certificate.service.certificate.application.service.CertificateProjectImportService;
import com.caidaocloud.certificate.service.certificate.application.service.CertificateProjectService;
import com.caidaocloud.certificate.service.certificate.infrastructure.util.ExcelUtil;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateProjectDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateProjectQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateUsageRecordImportDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateUsageRecordQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateUsageRecordSaveDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateUsageRecordUpdateDto;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.CertificateProjectPageVo;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.CertificateProjectVo;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.CertificateUsageRecordDetailVo;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.CertificateUsageRecordPageVo;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/api/certificate/v1/project")
@Api(tags = "证书项目管理接口")
@Slf4j
public class CertificateProjectController {

    @Autowired
    private CertificateProjectService certificateProjectService;
    @Resource
    private CertificateProjectImportService certificateProjectImportService;

    /**
     * 创建证书项目
     *
     * @param dto 包含证书项目信息的DTO对象
     * @return 创建成功后返回新项目的ID
     */
    @PostMapping("/create")
    @ApiOperation(value = "创建证书项目", notes = "接收一个包含证书项目信息的 DTO 对象作为请求体，创建新的证书项目并返回其 ID")
    public Result<String> createCertificateProject(@RequestBody CertificateProjectDto dto) {
        checkParam(dto);
        String projectId = certificateProjectService.createCertificateProject(dto);
        return Result.ok(projectId);
    }

    /**
     * 更新证书项目
     *
     * @param dto 包含更新信息的证书项目DTO对象
     */
    @PostMapping("/edit")
    @ApiOperation(value = "更新证书项目", notes = "接收一个包含证书项目更新信息的 DTO 对象作为请求体，更新指定证书项目的信息")
    public Result updateCertificateProject( @RequestBody CertificateProjectDto dto) {
        checkParam(dto);
        certificateProjectService.updateCertificateProject(dto);
        return Result.ok();

    }

    private void checkParam(@RequestBody CertificateProjectDto dto) {
        if (dto.getOrganize()==null) {
            throw  new ServerException("Project name cannot be empty");
        }
        if (dto.getEmpId()==null) {
            throw  new ServerException("Project leader cannot be empty");
        }
        if (dto.getLocation()==null) {
            throw  new ServerException("Project location cannot be empty");
        }
        if (dto.getStartDate() != null && dto.getEndDate() != null && dto.getStartDate() > dto.getEndDate()) {
            throw new ServerException("Start date cannot be later than end date");
        }
        if (dto.getCertificateStartDate() != null && dto.getCertificateEndDate() != null && dto.getCertificateStartDate() > dto.getCertificateEndDate()) {
            throw new ServerException("Start date cannot be later than end date");
        }
    }

    /**
     * 获取证书项目详情
     *
     * @param projectId 项目ID
     * @return 返回包含项目详细信息的CertificateProjectDto对象
     */
    @GetMapping("/detail")
    @ApiOperation(value = "获取证书项目详情")
    public Result<CertificateProjectVo> getCertificateProject(@RequestParam("projectId")  String projectId) {
        return Result.ok(certificateProjectService.getCertificateProjectVo(projectId));
    }

    /**
     * 删除证书项目
     *
     * @param projectId 待删除项目的ID
     */
    @DeleteMapping("/delete")
    @ApiOperation(value = "删除证书项目")
    public Result deleteCertificateProject(@RequestParam("projectId") String projectId) {
        certificateProjectService.deleteCertificateProject(projectId);
        return Result.ok();
    }

    /**
     * 创建新的证书使用记录项目
     */
    @PostMapping("/record/create")
    @ApiOperation(value = "创建新的证书使用记录")
    public Result createCertificateUsageRecord(@RequestBody CertificateUsageRecordSaveDto dto) {
        checkUsageRecordParam(dto);
        certificateProjectService.createCertificateUsageRecord(dto);
        return Result.ok();
    }


    /**
     * 导入新的证书使用记录项目
     */
    @PostMapping("/record/import")
    @ApiOperation(value = "导入新的证书使用记录")
    public Result importCertificateUsageRecord(@RequestBody CertificateUsageRecordImportDto dto) {
        certificateProjectImportService.importUsageRecord(dto);
        return Result.ok();
    }

    private void checkUsageRecordParam(CertificateUsageRecordSaveDto dto) {
        if (dto.getProjectId() == null) {
            throw new ServerException("Project cannot be empty");
        }
        if (dto.getEmpCertificate() == null || dto.getEmpCertificate().isEmpty()) {
            throw new ServerException("Emp cannot be empty");
        }
        if (dto.getType()==null) {
            throw  new ServerException("Certificate type cannot be empty");
        }
        if (dto.getStartDate() != null && dto.getEndDate() != null && dto.getStartDate() > dto.getEndDate()) {
            throw new ServerException("Start date cannot be later than end date");
        }
    }

    /**
     * 更新现有的证书使用记录项目
     */
    @PostMapping("/record/edit")
    @ApiOperation(value = "更新现有的证书使用记录")
    public Result updateCertificateUsageRecord( @RequestBody CertificateUsageRecordUpdateDto dto) {
        certificateProjectService.updateCertificateUsageRecord(dto);
        return Result.ok();
    }

    /**
     * 获取证书使用记录项目的详细信息
     * @return
     */
    @GetMapping("/record/detail")
    @ApiOperation(value = "获取证书使用记录的详细信息")
    public Result<CertificateUsageRecordDetailVo> getCertificateUsageRecordDetail(@RequestParam("bid") String bid) {
        return Result.ok(certificateProjectService.getCertificateUsageRecordDetail(bid));
    }

    /**
     * 删除指定的证书使用记录项目
     */
    @DeleteMapping("/record/delete")
    @ApiOperation(value = "删除指定的证书使用记录")
    public Result deleteCertificateUsageRecord(@RequestParam("bid") String bid) {
        certificateProjectService.deleteCertificateUsageRecord(bid);
        return Result.ok();
    }

    /**
     * 根据查询条件获取所有证书使用记录项目的分页结果
     */
    @PostMapping("/record/page")
    @ApiOperation(value = "根据查询条件获取所有证书使用记录的分页结果")
    public Result<PageResult<CertificateUsageRecordPageVo>> getAllCertificateUsageRecordsByProject(@RequestBody CertificateUsageRecordQueryDto dto) {
        if (dto.getPageSize()>1000) {
            throw new ServerException("Page size cannot be greater than 1000");
        }
        return Result.ok(certificateProjectService.getAllCertificateUsageRecordsByProject(dto));
    }


    @SneakyThrows
    @PostMapping("/record/export")
    @ApiOperation(value = "根据查询条件获取所有证书使用记录的分页结果")
    public void exportAllCertificateUsageRecordsByProject(HttpServletResponse response,@RequestBody CertificateUsageRecordQueryDto dto) {
        try(OutputStream out = response.getOutputStream()){
            Workbook wb = certificateProjectService.exportAllCertificateUsageRecordsByProject(dto);
            ExcelUtil.downloadExcel(response, out, wb,"证书登记使用情况");
        } catch (Exception e){
            log.error("Export certificate usage records err,{}", e);
            throw e;
        }
    }

    

    /**
     * 根据查询条件获取证书项目的分页结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "根据查询条件获取证书项目的分页结果")
    public Result<PageResult<CertificateProjectPageVo>> getCertificateProjectPage(@RequestBody CertificateProjectQueryDto dto) {
        if (dto.getPageSize()>1000) {
            throw new ServerException("Page size cannot be greater than 1000");
        }
        return Result.ok(certificateProjectService.getCertificateProjectPage(dto));
    }
    @SneakyThrows
    @PostMapping("/export")
    @ApiOperation(value = "根据查询条件获取证书项目的分页结果")
    public void exportCertificateProjectPage(HttpServletResponse response,@RequestBody CertificateProjectQueryDto dto) {
        try(OutputStream out = response.getOutputStream()){
            Workbook wb = certificateProjectService.exportCertificateProjectPage(dto);
            ExcelUtil.downloadExcel(response, out, wb,"证书项目");
        } catch (Exception e){
            log.error("Export certificate project err,{}", e);
            throw e;
        }
    }
}
