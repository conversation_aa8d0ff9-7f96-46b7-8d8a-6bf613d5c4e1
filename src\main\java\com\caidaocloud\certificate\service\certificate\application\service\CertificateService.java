package com.caidaocloud.certificate.service.certificate.application.service;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.caidaocloud.certificate.service.certificate.domain.base.enums.BusinessEventTypeEnum;
import com.caidaocloud.certificate.service.certificate.domain.base.util.TagProperty;
import com.caidaocloud.certificate.service.certificate.domain.entity.CeritificateAndEmpDo;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateDo;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateTypeDo;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICeritificateAndEmpRepository;
import com.caidaocloud.certificate.service.certificate.domain.repository.IEmpInfoRepository;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CeritificateAndEmpQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateTypeQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.CertificateExcelVo;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.util.*;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/9 18:47
 **/
@Service
@Slf4j
public class CertificateService {

    @Resource
    private CertificateDo certificateDo;
    @Resource
    private CertificateTypeDo certificateTypeDo;
    @Autowired
    private IEmpInfoRepository empInfoRepository;


    /**
     * 新增
     * @param data
     */
    public String add(CertificateDo data) {
        cheeck(data);
        return certificateDo.save(data);
    }

    private void cheeck(CertificateDo data) {
        data.getTypeBid();
        CertificateTypeDo certificateTypeDo = this.certificateTypeDo.selectById(data.getTypeBid());
        PreCheck.preCheckArgument(certificateTypeDo.getLevel()!=2, "非证书子类不可以增加证书");
    }


    public void update(CertificateDo dto) {
        cheeck(dto);
        certificateDo.update(dto);
    }

    public List<CertificateDo> selectList(CertificateQueryDto dto) {
        return certificateDo.selectList(dto);
    }

    public void delete(CertificateDo data) {
        //员工信息校验
        PreCheck.preCheckArgument(checkEmp(data), "该证书已在员工信息中维护，不允许删除");
        certificateDo.delete(data);

    }

    /**
     * 证书是否被人员证书登记
     * @param data
     */
    private Boolean checkEmp(CertificateDo data) {
        CeritificateAndEmpQueryDto dto =new CeritificateAndEmpQueryDto();
        dto.setCeritifiCateBid(data.getBid());
        List<CeritificateAndEmpDo> doList = SpringUtil.getBean(ICeritificateAndEmpRepository.class).queryList(dto);
        return CollectionUtils.isEmpty(doList)?false:true;

    }

    public void enable(CertificateDo data) {
        certificateDo.updateStatus(data, BusinessEventTypeEnum.ENABLE);
    }

    public void disable(CertificateDo data) {
        PreCheck.preCheckArgument(ObjectUtil.isEmpty(certificateDo.selectById(data.getBid())), "证书不存在");
        // 员工信息维护 校验
        PreCheck.preCheckArgument(checkEmp(data), "该证书在员工信息中仍存在生效中的数据，不允许停用");
        certificateDo.updateStatus(data, BusinessEventTypeEnum.DISABLE);
    }

    /**
     * 导入
     * @param file
     * @param tenantId
     * @return
     */
    public List<String> importFile(MultipartFile file, String tenantId) {
        ImportParams params = new ImportParams();
        params.setTitleRows(0);
        params.setHeadRows(1);
        List<CertificateExcelVo> importData;
        try {
            importData = ExcelImportUtil.importExcel(file.getInputStream(), CertificateExcelVo.class, params);
        } catch (Exception e) {
            log.error("导入数据有误 ,msg = {}", e.getMessage(), e);
            throw ServerException.globalException(ErrorMessage.fromCode("导入数据有误,读取表格或者初始化数据报错"));
        }

        List<String> importFailGrowthRecordIdList = new ArrayList<>();

        importData.forEach(item -> {
            try {
                // 逻辑根据代码进行覆盖
                //证书大类
                CertificateTypeQueryDto dto=new CertificateTypeQueryDto();
                dto.setCode(item.getTypeCode());
                dto.setLevel(1);
                List<CertificateTypeDo> typelist = certificateTypeDo.selectList(dto);
                //证书子类
                dto.setCode(item.getTypeSubCode());
                dto.setLevel(2);
                List<CertificateTypeDo> typeSublist = certificateTypeDo.selectList(dto);
                //证书
                CertificateQueryDto queryDto=new CertificateQueryDto();
                queryDto.setNameOrCode(item.getCode());
                List<CertificateDo> certificateDos = certificateDo.selectList(queryDto);
                //证书大类
                if (CollectionUtils.isNotEmpty(typelist)){
                    updateTypeEntity(item,typelist,typeSublist,certificateDos);
                }else {
                    //创造证书大类+子类+证书
                    createTypeEntity(item);
                }
            } catch (Exception e) {
                log.error("导入数据有误 ,msg: {},导入数据id为: {}", e.getMessage(), item.getBid());
                importFailGrowthRecordIdList.add(item.getBid());
            }
        });

        return importFailGrowthRecordIdList;

    }

    private void updateTypeEntity(CertificateExcelVo item, List<CertificateTypeDo> typelist, List<CertificateTypeDo> typeSublist, List<CertificateDo> certificateDos) {
        CertificateTypeDo upTypeDo = new CertificateTypeDo();
        BeanUtil.copyProperties(typelist.get(0),upTypeDo);
        upTypeDo.setName(item.getTypeName());
        certificateTypeDo.update(upTypeDo);
        //证书子类
        if (CollectionUtils.isNotEmpty(typeSublist)){
            CertificateTypeDo upSubTypeDo = new CertificateTypeDo();
            BeanUtil.copyProperties(typeSublist.get(0),upSubTypeDo);
            upSubTypeDo.setName(item.getTypeSubName());
            certificateTypeDo.update(upSubTypeDo);
            //证书
            if (CollectionUtils.isNotEmpty(certificateDos)){
                CertificateDo upCertificateDo = new CertificateDo();
                BeanUtil.copyProperties(certificateDos.get(0),upCertificateDo);
                upCertificateDo.setName(item.getName());
                certificateDo.update(upCertificateDo);
                item.setBid(upCertificateDo.getBid());
            }else{
                CertificateDo createCertificateDo = new CertificateDo();
                BeanUtil.copyProperties(certificateDos.get(0),createCertificateDo);
                createCertificateDo.setName(item.getName());
                createCertificateDo.setTypeBid(typeSublist.get(0).getBid());
                String bid = certificateDo.save(createCertificateDo);
                item.setBid(bid);
            }
        }else {
            //创造证书子类+证书
            createTypeAndEntity(item,typelist);
        }
    }

    /**
     * 创建证书子类+证书的方法
     * @param item
     * @param typelist
     */
    private void createTypeAndEntity(CertificateExcelVo item, List<CertificateTypeDo> typelist) {
        CertificateTypeDo createTypeDo = new CertificateTypeDo();
        createTypeDo.setName(item.getTypeSubName());
        createTypeDo.setCode(item.getTypeSubCode());
        createTypeDo.setPBid(typelist.get(0).getBid());
        createTypeDo.setLevel(2);
        String subTypeBid = certificateTypeDo.save(createTypeDo);
        //创建证书
        CertificateDo createCertificateDo = new CertificateDo();
        createCertificateDo.setName(item.getName());
        createCertificateDo.setCode(item.getCode());
        createCertificateDo.setTypeBid(subTypeBid);
        String bid = certificateDo.save(createCertificateDo);
        item.setBid(bid);
    }

    /**
     * 创造证书大类+证书子类+证书的方法
     * @param item
     */
    private void createTypeEntity(CertificateExcelVo item) {
        CertificateTypeDo createTypeDo = new CertificateTypeDo();
        createTypeDo.setName(item.getTypeName());
        createTypeDo.setCode(item.getTypeCode());
        createTypeDo.setLevel(1);
        certificateTypeDo.save(createTypeDo);
        createTypeAndEntity(item, Lists.list(createTypeDo));
    }

    //此处bid存取的应给为typeid值
    public  List<CertificateExcelVo> selectExportRecordPage(CertificateDto dto) {
//        PreCheck.preCheckArgument(StringUtils.isEmpty(dto.getTypeBid()), "子类bid为空");
        if (StringUtils.isEmpty(dto.getTypeBid())){
            return loadDataForAll();
        }
        return loadData(dto);


    }

    /**
     * 全量导出
     * @return
     */
    private List<CertificateExcelVo> loadDataForAll() {
        List<CertificateExcelVo> excelVos = Lists.list();
        CertificateQueryDto dto=new CertificateQueryDto();
        dto.setStatus("0");
        List<CertificateDo> certificateDos = selectList(dto);
        for (CertificateDo cerDo : certificateDos) {
            CertificateExcelVo exportVo=new CertificateExcelVo();
            exportVo.setName(cerDo.getName());
            exportVo.setCode(cerDo.getCode());
            exportVo.setStatus(Optional.ofNullable(cerDo.getStatus()).map(EnumSimple::getText).orElse(null));
            CertificateTypeDo subTypeDo = certificateTypeDo.selectById(cerDo.getTypeBid());
            if (ObjectUtil.isNotEmpty(subTypeDo)){
                exportVo.setTypeSubCode(subTypeDo.getCode());
                exportVo.setTypeSubName(subTypeDo.getName());
                if (subTypeDo.getLevel()==2){
                    CertificateTypeDo typeDo = certificateTypeDo.selectById(subTypeDo.getPBid());
                    if (ObjectUtil.isNotEmpty(typeDo)){
                        exportVo.setTypeCode(typeDo.getCode());
                        exportVo.setTypeName(typeDo.getName());
                        excelVos.add(exportVo);
                    }else{
                        log.error(typeDo.toString(),"不存在证书类型");continue;
                    }
                }
                log.error(subTypeDo.getName()+"证书level ！= 2");
            }else{
                log.error(cerDo.toString(),"不存在证书子类");
                continue;
            }

        }
        return excelVos;
    }

    private List<CertificateExcelVo> loadData(CertificateDto dto) {
        List<CertificateExcelVo> list= Lists.list();

        //证书
        CertificateQueryDto queryDto=new CertificateQueryDto();
        queryDto.setTypeBid(dto.getTypeBid());
        List<CertificateDo> certificateDos = this.certificateDo.selectList(queryDto);
        certificateDos.forEach(t->{
            CertificateExcelVo exportVo=new CertificateExcelVo();
            //应该为子类
            CertificateTypeDo subTypeDo = this.certificateTypeDo.selectById(dto.getTypeBid());
            PreCheck.preCheckArgument(subTypeDo.getLevel()!=2, "该bid不是证书子类");
            exportVo.setTypeSubCode(subTypeDo.getCode());
            exportVo.setTypeSubName(subTypeDo.getName());
            PreCheck.preCheckArgument(StringUtils.isEmpty(subTypeDo.getPBid()), "不存在上级证书类型");
            //证书大类
            CertificateTypeDo typeDo = this.certificateTypeDo.selectById(subTypeDo.getPBid());
            exportVo.setTypeCode(typeDo.getCode());
            exportVo.setTypeName(typeDo.getName());
            exportVo.setName(t.getName());
            exportVo.setCode(t.getCode());
            list.add(exportVo);
        });
        return list;
    }

    public List<TagProperty> installCertificateHeader() {
        List<TagProperty> list = new ArrayList<>();
        addTagPropertyToList(list, "typeCode", "证书类别代码", 1);
        addTagPropertyToList(list, "typeName", "证书类别名称", 2);
        addTagPropertyToList(list, "typeSubCode", "证书子类代码", 3);
        addTagPropertyToList(list, "typeSubName", "证书子类名称", 4);
        addTagPropertyToList(list, "code", "证书代码", 5);
        addTagPropertyToList(list, "name", "证书名称", 6);
        addTagPropertyToList(list, "status", "状态", 7);

        return list;
    }
    public void addTagPropertyToList(List<TagProperty> list, String property, String propertyTxt, int order) {
        list.add(new TagProperty(property, propertyTxt, order));
    }

    public CertificateDo getById(String bid) {
        return certificateDo.selectById(bid);
    }

    public PageResult<CertificateDo> pageList(CertificateQueryDto queryDto) {
//        if (StringUtils.isEmpty(queryDto.getNameOrCode())){
//            return certificateDo.pageList(queryDto);
//        }
        HttpServletRequest request = WebUtil.getRequest();
        String header = request.getHeader("Accept-Language");

        PageResult<CertificateDo> list = certificateDo.pageList(queryDto);
        list.getItems().forEach(t->{
            Map map = FastjsonUtil.toObject(t.getI18nName(), Map.class);
            if (map!=null && map.get(header)!=null){
                t.setName(map.get(header).toString());
            }

        });
        return list;
    }
}
