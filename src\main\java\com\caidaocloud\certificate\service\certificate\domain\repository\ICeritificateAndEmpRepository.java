package com.caidaocloud.certificate.service.certificate.domain.repository;

import com.caidaocloud.certificate.service.certificate.application.dto.CertificateCardDto;
import com.caidaocloud.certificate.service.certificate.domain.base.repository.BaseRepository;
import com.caidaocloud.certificate.service.certificate.domain.entity.CeritificateAndEmpDo;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CeritificateAndEmpQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.QualifiedPerQueryDto;
import com.caidaocloud.dto.PageResult;

import java.util.List;

public interface ICeritificateAndEmpRepository extends BaseRepository<CeritificateAndEmpDo> {

    List<CeritificateAndEmpDo> queryList(CeritificateAndEmpQueryDto data);

    PageResult<CeritificateAndEmpDo> queryPage(CeritificateAndEmpQueryDto data, int pageSize, int pageNo);

    PageResult<CertificateCardDto> queryCardPage(CeritificateAndEmpQueryDto data, int pageSize, int pageNo, boolean filterAvailable);

    PageResult<CeritificateAndEmpDo> queryTablePage(CeritificateAndEmpQueryDto data, int pageSize, int pageNo);

    /**
     *用于证书资格一览查询
     */
    List<CeritificateAndEmpDo> select(QualifiedPerQueryDto data);

    List<CeritificateAndEmpDo> listByEmpId(String empId, String certificate);

	List<CeritificateAndEmpDo> selectBatchIds(List<String> bid);
}
